/* Main Styles for Naroop Social Media Platform - Modern Dark Theme */

/* ===== COMPONENT BASE CLASSES ===== */

/* Card Base - Consistent styling for all card-like components */
.card-base {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.card-base:hover {
    background: var(--bg-card-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Card Padding Variants */
.card-padding {
    padding: var(--card-padding);
}

.card-padding-sm {
    padding: var(--spacing-lg);
}

.card-padding-lg {
    padding: var(--spacing-3xl);
}

/* Card Positioning */
.card-sticky {
    position: sticky;
    top: 110px;
    height: fit-content;
}

/* Button Base Classes */
.btn-base {
    border: none;
    border-radius: var(--radius-xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-button);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

.btn-sm {
    padding: var(--button-padding-sm);
    font-size: var(--font-size-sm);
}

.btn-md {
    padding: var(--button-padding-md);
    font-size: var(--font-size-base);
}

.btn-lg {
    padding: var(--button-padding-lg);
    font-size: var(--font-size-lg);
}

/* Reset and Base Styles - Updated for modern dark theme */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container and Layout - Updated for modern design */
.container {
    max-width: var(--breakpoint-2xl);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Grid and Flexbox Layouts */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Header Styles - Updated for modern dark theme */
.header {
    background: var(--header-bg);
    backdrop-filter: var(--header-backdrop-blur);
    border-bottom: var(--card-border);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.logo {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--shadow-glow);
    cursor: pointer;
    transition: var(--transition-normal);
}

.logo:hover {
    transform: scale(1.05);
}

/* Navigation Buttons - Updated for modern theme */
.nav-buttons {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-btn {
    background: var(--gradient-button);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: var(--font-size-base);
    box-shadow: var(--shadow-glow);
}

.nav-btn:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

.nav-btn.primary {
    background: var(--gradient-button);
    color: var(--text-primary);
}

.nav-btn.primary:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

/* Main Content Layout - Updated for modern design */
.main-content {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr 320px;
    gap: var(--spacing-2xl);
    padding: var(--spacing-2xl) 0;
    min-height: calc(100vh - var(--header-height));
}

/* Sidebar Styles - Updated for modern dark theme */
.sidebar {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--card-padding);
    height: fit-content;
    position: sticky;
    top: 110px;
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
}

.sidebar h3 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

/* Navigation Items - Updated for modern design */
.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    border: 1px solid transparent;
}

.nav-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-focus);
    transform: translateX(var(--spacing-xs));
}

.nav-item.active {
    background: var(--gradient-card);
    border-color: var(--border-accent);
}

.nav-icon {
    font-size: 20px;
    color: var(--color-primary);
}

/* Legacy sidebar-item support */
.sidebar-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    border: 1px solid transparent;
}

.sidebar-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-focus);
    transform: translateX(var(--spacing-xs));
}

.sidebar-item.active {
    background: var(--gradient-card);
    border-color: var(--border-accent);
}

/* Feed Section - Updated for modern dark theme */
.feed-section, .feed {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: 0;
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.feed-header {
    padding: var(--card-padding);
    border-bottom: var(--card-border);
}

.feed-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(45deg, #ffffff, var(--color-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Refresh Button - Updated for modern dark theme */
.refresh-btn {
    background: var(--bg-input);
    color: var(--text-muted);
    border: var(--input-border);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.refresh-btn:hover {
    background: var(--bg-card-hover);
    color: var(--color-primary);
    border-color: var(--border-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-btn::before {
    content: '🔄';
    font-size: var(--font-size-sm);
}

/* Content Sections */
.content-section {
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

.content-section:not(.active) {
    display: none !important;
    opacity: 0;
}

.content-section.active,
.feed.content-section.active {
    display: flex !important;
    flex-direction: column;
    opacity: 1;
    min-height: 400px;
}

/* Mobile Navigation */
.mobile-nav-item {
    transition: all 0.3s ease;
}

.mobile-nav-item.touch-active {
    background: rgba(139, 69, 19, 0.2);
    transform: scale(0.95);
}

/* Loading States - Updated for modern theme */
.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.hidden { display: none !important; }
.visible { display: block !important; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility - Updated for modern theme */
.nav-item:focus,
.sidebar-item:focus,
.nav-btn:focus,
.refresh-btn:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* High contrast mode support - Updated for modern theme */
@media (prefers-contrast: high) {
    .nav-item:hover,
    .sidebar-item:hover {
        background: var(--bg-card-hover);
        border-color: var(--border-accent);
    }

    .nav-btn {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Modern Modal System - Updated for dark theme */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
    z-index: var(--z-modal-backdrop);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    padding: var(--card-padding);
    border-radius: var(--radius-xl);
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    width: 90%;
    max-width: 500px;
    position: relative;
    transform: scale(0.95);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-xl);
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-close-button {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--bg-input);
    border: var(--input-border);
    border-radius: var(--radius-full);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-primary);
    transition: var(--transition-normal);
}

.modal-close-button:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
}
